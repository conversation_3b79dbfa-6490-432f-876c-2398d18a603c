# GLM-Highlight 性能分析报告

## 1. 性能瓶颈分析

### 性能测试数据
- **_findMatches**: 34.7% (1,178.0 ms)
- **_processTextNode**: 34.7% (包含_findMatches)
- **highlight**: 34.7% (包含_processTextNode)
- **processBatch**: 34.7% (包含highlight)

### 调用链分析
```
content-action.js:60 (processBatch)
  └── highlighter.js:172 (highlight)
      └── highlighter.js:181 (_processTextNode) [forEach循环]
          └── highlighter.js:295 (_findMatches)
              └── highlighter.js:377 (_findAllOccurrences) [循环每个关键词]
                  └── KMP算法或indexOf
```

## 2. 核心问题识别

### 问题1: _findMatches 的多重循环嵌套
```javascript
// 当前实现的问题
for (const keyword of sortedKeywords) {  // O(k) k=关键词数量
  const positions = this._findAllOccurrences(text, keywordText); // O(n*m)
  for (const index of positions) { // O(p) p=匹配位置数
    // 重叠检查和插入操作
  }
}
// 总复杂度: O(k * n * m * p)
```

### 问题2: KMP算法的使用场景不当
- 对于短关键词（≤3字符），使用indexOf
- 对于长关键词，使用KMP算法
- KMP算法的预处理开销对于短文本可能不值得

### 问题3: 缺少有效缓存
- 每个文本节点都要重新查找所有匹配
- 相同的文本内容会重复计算
- 没有利用文本内容的相似性

### 问题4: 重叠检查效率低下
- 使用二分查找插入排序区间
- 每次插入都要移动数组元素

## 3. 优化方案

### 方案A: 缓存优化（快速见效）
1. 为_findMatches添加结果缓存
2. 使用文本哈希作为缓存键
3. LRU缓存策略避免内存泄漏

### 方案B: 算法优化（根本解决）
1. 使用Aho-Corasick算法进行多模式匹配
2. 一次遍历找出所有关键词的所有匹配
3. 优化重叠处理逻辑

### 方案C: 批处理优化（辅助提升）
1. 合并相邻的文本节点后再处理
2. 对超长文本使用Web Worker
3. 动态调整批处理大小

## 4. 建议实施顺序

1. **第一阶段**：添加匹配缓存（预期提升30-50%）
2. **第二阶段**：优化查找算法（预期提升40-60%）
3. **第三阶段**：优化批处理逻辑（预期提升10-20%）

## 5. 性能指标

### 当前性能
- 处理时间：1,178ms
- 关键词数量：未知
- 文本节点数：未知

### 优化目标
- 处理时间：< 500ms
- 缓存命中率：> 60%
- 内存占用：< 50MB
