// 优化后的 _findMatches 相关方法
// 这些方法可以直接替换 highlighter.js 中的对应方法

// 1. 在构造函数中添加缓存属性（在现有缓存属性后添加）
// this.matchCache = new Map();
// this.MAX_MATCH_CACHE = 200;

// 2. 替换 _findMatches 方法
_findMatches(text, keywords) {
  try {
    // 快速返回空结果
    if (!text || !keywords || keywords.length === 0) {
      return [];
    }
    
    // 生成缓存键 - 使用简单的哈希
    const cacheKey = this._generateMatchCacheKey(text, keywords);
    
    // 检查缓存
    if (this.matchCache && this.matchCache.has(cacheKey)) {
      if (this.config.debug) {
        console.log('[Cache Hit] findMatches');
      }
      return [...this.matchCache.get(cacheKey)]; // 返回副本避免修改缓存
    }
    
    // 预过滤关键词 - 快速排除不可能匹配的关键词
    const textLower = text.toLowerCase();
    const activeKeywords = [];
    
    for (const keyword of keywords) {
      const keywordText = keyword.words || keyword;
      if (!keywordText) continue;
      
      // 快速检查：如果文本中不包含关键词的任何字符，跳过
      const keywordLower = keywordText.toLowerCase();
      // 使用 indexOf 进行快速预检
      if (textLower.indexOf(keywordLower) === -1) {
        continue;
      }
      
      activeKeywords.push({
        original: keyword,
        text: keywordText,
        textLower: keywordLower,
        length: keywordText.length
      });
    }
    
    // 如果没有可能的匹配，早期返回
    if (activeKeywords.length === 0) {
      this._addToMatchCache(cacheKey, []);
      return [];
    }
    
    // 按长度降序排序，优先匹配长关键词
    activeKeywords.sort((a, b) => b.length - a.length);
    
    // 使用优化的多模式匹配
    const matches = this._findAllMatchesOptimized(text, textLower, activeKeywords);
    
    // 缓存结果
    this._addToMatchCache(cacheKey, matches);
    
    return matches;
  } catch (error) {
    Utils.handleError(error, "findMatches", "HIGHLIGHT");
    return [];
  }
}

// 3. 新增：生成缓存键
_generateMatchCacheKey(text, keywords) {
  // 使用文本长度和前30个字符作为键的一部分
  const textPart = `${text.length}_${text.substring(0, 30)}`;
  // 使用关键词数量和第一个关键词作为键的一部分
  const keywordsPart = `${keywords.length}_${(keywords[0]?.words || keywords[0] || '').substring(0, 10)}`;
  return `${textPart}_${keywordsPart}`;
}

// 4. 新增：优化的多模式匹配
_findAllMatchesOptimized(text, textLower, keywords) {
  const matches = [];
  const usedRanges = [];
  
  // 对每个关键词进行匹配
  for (const keyword of keywords) {
    let index = 0;
    const pattern = keyword.textLower;
    const patternLength = keyword.length;
    
    // 使用 indexOf 进行快速搜索（比 KMP 在 JavaScript 中更快）
    while ((index = textLower.indexOf(pattern, index)) !== -1) {
      const end = index + patternLength;
      
      // 检查是否与已有匹配重叠
      let overlaps = false;
      for (const [rangeStart, rangeEnd] of usedRanges) {
        if (!(end <= rangeStart || index >= rangeEnd)) {
          overlaps = true;
          break;
        }
      }
      
      if (!overlaps) {
        // 如果需要大小写敏感匹配，进行二次验证
        let shouldAdd = true;
        if (this.options.caseSensitive) {
          const actualText = text.substring(index, end);
          shouldAdd = actualText === keyword.text;
        }
        
        if (shouldAdd) {
          matches.push({
            start: index,
            end: end,
            text: text.substring(index, end),
            keyword: keyword.original
          });
          
          // 添加到已使用范围（保持排序）
          this._insertUsedRange(usedRanges, index, end);
        }
      }
      
      index++; // 继续搜索下一个位置
    }
  }
  
  return matches;
}

// 5. 新增：插入已使用范围（保持排序）
_insertUsedRange(ranges, start, end) {
  // 找到插入位置
  let insertIndex = 0;
  for (let i = 0; i < ranges.length; i++) {
    if (ranges[i][0] > start) {
      insertIndex = i;
      break;
    }
    insertIndex = i + 1;
  }
  ranges.splice(insertIndex, 0, [start, end]);
}

// 6. 新增：添加到缓存
_addToMatchCache(key, matches) {
  if (!this.matchCache) {
    this.matchCache = new Map();
  }
  
  // LRU 缓存策略
  if (this.matchCache.size >= (this.MAX_MATCH_CACHE || 200)) {
    // 删除最早的缓存项
    const firstKey = this.matchCache.keys().next().value;
    this.matchCache.delete(firstKey);
  }
  
  this.matchCache.set(key, matches);
}

// 7. 修改 clearCache 方法，添加匹配缓存清理
clearCache() {
  this.nodeStates = new WeakMap();
  this.patternCache.clear();
  this.patternCacheOrder = [];
  this.fragmentCache = new WeakMap();
  this._keywordCache = null;
  if (this.matchCache) {
    this.matchCache.clear();
  }
}
