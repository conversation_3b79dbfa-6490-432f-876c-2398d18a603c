<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GLM-Highlight 性能测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .performance-stats {
            background: #e8f5e8;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-family: monospace;
        }
        .test-content {
            columns: 2;
            column-gap: 20px;
            text-align: justify;
        }
        .large-content {
            columns: 3;
            column-gap: 15px;
        }
        .dynamic-content {
            min-height: 200px;
            border: 2px dashed #ccc;
            padding: 10px;
            margin: 10px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }
        .metric-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .metric-value {
            font-size: 24px;
            font-weight: bold;
            color: #007cba;
        }
        .metric-label {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>GLM-Highlight 性能测试页面</h1>
        
        <div class="performance-stats" id="performanceStats">
            <strong>性能统计:</strong> 等待测试开始...
        </div>

        <div class="metrics" id="metricsContainer">
            <div class="metric-card">
                <div class="metric-value" id="totalTime">0ms</div>
                <div class="metric-label">总处理时间</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="frameTime">0ms</div>
                <div class="metric-label">平均帧时间</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="taskCount">0</div>
                <div class="metric-label">任务数量</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" id="cacheHits">0%</div>
                <div class="metric-label">缓存命中率</div>
            </div>
        </div>

        <div class="test-section">
            <h2>测试控制</h2>
            <button onclick="startPerformanceTest()">开始性能测试</button>
            <button onclick="addDynamicContent()">添加动态内容</button>
            <button onclick="clearContent()">清空内容</button>
            <button onclick="showTaskSchedulerStatus()">显示调度器状态</button>
        </div>

        <div class="test-section">
            <h2>基础文本内容测试</h2>
            <div class="test-content">
                <p>这是一个包含大量文本的测试页面，用于验证GLM-Highlight插件的性能优化效果。我们需要测试在处理大量文本节点时，新的任务调度器是否能够有效地将长时间的阻塞操作分解为多个小的时间片。</p>
                
                <p>在之前的性能分析中，我们发现_findMatches函数占用了34.7%的处理时间，总计1,178.0ms。这种长时间的主线程阻塞会导致页面卡顿和用户体验下降。通过引入统一的任务调度器，我们期望能够将这种阻塞分散到多个12ms的时间片中。</p>

                <p>任务调度器的核心优势包括：1) 优先级队列管理，确保重要任务优先执行；2) 任务去重机制，避免重复处理相同内容；3) 时间片控制，防止主线程长时间阻塞；4) 统一的异步任务管理，减少并发竞争。</p>

                <p>测试关键词包括：性能、优化、任务、调度器、时间片、主线程、阻塞、缓存、命中率、并发、竞争、文本、节点、处理、分析、验证、测试、效果、改善、提升。</p>
            </div>
        </div>

        <div class="test-section">
            <h2>大量文本内容测试</h2>
            <div class="large-content" id="largeContent">
                <!-- 这里会通过JavaScript动态生成大量内容 -->
            </div>
        </div>

        <div class="test-section">
            <h2>动态内容测试</h2>
            <div class="dynamic-content" id="dynamicContent">
                <p>这里会动态添加内容来测试MutationObserver的性能...</p>
            </div>
        </div>
    </div>

    <script>
        // 性能测试工具
        class PerformanceMonitor {
            constructor() {
                this.startTime = 0;
                this.frameCount = 0;
                this.totalFrameTime = 0;
                this.measurements = [];
            }

            start() {
                this.startTime = performance.now();
                this.frameCount = 0;
                this.totalFrameTime = 0;
                this.measurements = [];
                console.log('性能测试开始...');
            }

            measureFrame() {
                const frameTime = performance.now();
                if (this.frameCount > 0) {
                    const frameDuration = frameTime - this.lastFrameTime;
                    this.totalFrameTime += frameDuration;
                    this.measurements.push(frameDuration);
                }
                this.lastFrameTime = frameTime;
                this.frameCount++;
            }

            getResults() {
                const totalTime = performance.now() - this.startTime;
                const avgFrameTime = this.frameCount > 1 ? this.totalFrameTime / (this.frameCount - 1) : 0;
                const maxFrameTime = Math.max(...this.measurements);
                
                return {
                    totalTime: totalTime.toFixed(2),
                    avgFrameTime: avgFrameTime.toFixed(2),
                    maxFrameTime: maxFrameTime.toFixed(2),
                    frameCount: this.frameCount,
                    measurements: this.measurements
                };
            }
        }

        const monitor = new PerformanceMonitor();

        // 生成测试内容
        function generateLargeContent() {
            const container = document.getElementById('largeContent');
            const testWords = ['性能', '优化', '任务', '调度器', '时间片', '主线程', '阻塞', '缓存', '命中率', '并发', '竞争', '文本', '节点', '处理', '分析', '验证', '测试', '效果', '改善', '提升'];
            
            let content = '';
            for (let i = 0; i < 100; i++) {
                const paragraph = [];
                for (let j = 0; j < 50; j++) {
                    const word = testWords[Math.floor(Math.random() * testWords.length)];
                    paragraph.push(word);
                }
                content += `<p>段落${i + 1}: ${paragraph.join('、')}。这是一个包含大量关键词的测试段落，用于验证高亮性能。</p>`;
            }
            
            container.innerHTML = content;
        }

        // 开始性能测试
        function startPerformanceTest() {
            monitor.start();
            generateLargeContent();
            
            // 监控帧性能
            function frameMonitor() {
                monitor.measureFrame();
                if (monitor.frameCount < 100) {
                    requestAnimationFrame(frameMonitor);
                } else {
                    displayResults();
                }
            }
            
            requestAnimationFrame(frameMonitor);
        }

        // 显示测试结果
        function displayResults() {
            const results = monitor.getResults();
            
            document.getElementById('totalTime').textContent = results.totalTime + 'ms';
            document.getElementById('frameTime').textContent = results.avgFrameTime + 'ms';
            document.getElementById('taskCount').textContent = results.frameCount;
            
            // 尝试获取插件的性能统计
            if (window.highlighter && window.highlighter.getPerformanceReport) {
                const report = window.highlighter.getPerformanceReport();
                document.getElementById('cacheHits').textContent = report.cacheHitRate || '0%';
                
                document.getElementById('performanceStats').innerHTML = `
                    <strong>性能统计:</strong><br>
                    总处理时间: ${results.totalTime}ms<br>
                    平均帧时间: ${results.avgFrameTime}ms<br>
                    最大帧时间: ${results.maxFrameTime}ms<br>
                    帧数: ${results.frameCount}<br>
                    缓存命中率: ${report.cacheHitRate || '0%'}<br>
                    处理节点数: ${report.totalProcessed || 0}<br>
                    匹配数: ${report.totalMatches || 0}
                `;
            }
            
            console.log('性能测试结果:', results);
        }

        // 添加动态内容
        function addDynamicContent() {
            const container = document.getElementById('dynamicContent');
            const timestamp = new Date().toLocaleTimeString();
            const newContent = document.createElement('p');
            newContent.textContent = `${timestamp}: 新添加的动态内容，包含性能、优化、测试等关键词。`;
            container.appendChild(newContent);
        }

        // 清空内容
        function clearContent() {
            document.getElementById('largeContent').innerHTML = '';
            document.getElementById('dynamicContent').innerHTML = '<p>内容已清空...</p>';
        }

        // 显示任务调度器状态
        function showTaskSchedulerStatus() {
            if (window.Utils && window.Utils.TaskScheduler) {
                // 这里需要访问全局的taskScheduler实例
                console.log('任务调度器已加载');
                alert('任务调度器状态请查看控制台');
            } else {
                alert('任务调度器未找到');
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('性能测试页面已加载');
            
            // 检查插件是否已加载
            setTimeout(() => {
                if (window.highlighter) {
                    console.log('GLM-Highlight插件已检测到');
                } else {
                    console.log('GLM-Highlight插件未检测到');
                }
            }, 1000);
        });
    </script>
</body>
</html>
