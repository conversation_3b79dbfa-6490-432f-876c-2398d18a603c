// 性能优化补丁 - 将优化后的方法注入到现有的TextHighlighter实例中
(function() {
  // 等待highlighter实例创建
  const patchHighlighter = () => {
    if (!window.highlighter || !window.TextHighlighter) {
      setTimeout(patchHighlighter, 100);
      return;
    }

    // 添加新的缓存属性
    window.highlighter.matchCache = new Map();
    window.highlighter.MAX_MATCH_CACHE = 500;
    window.highlighter.matchCacheOrder = [];

    // 保存原始的clearCache方法
    const originalClearCache = window.highlighter.clearCache;

    // 重写clearCache方法
    window.highlighter.clearCache = function() {
      originalClearCache.call(this);
      this.matchCache.clear();
      this.matchCacheOrder = [];
    };

    // 替换_findMatches方法
    window.highlighter._findMatches = function(text, keywords) {
      try {
        // 生成缓存键
        const cacheKey = this._generateMatchCacheKey(text, keywords);
        
        // 检查缓存
        if (this.matchCache.has(cacheKey)) {
          // 更新LRU顺序
          const index = this.matchCacheOrder.indexOf(cacheKey);
          if (index > -1) {
            this.matchCacheOrder.splice(index, 1);
          }
          this.matchCacheOrder.push(cacheKey);
          return this.matchCache.get(cacheKey);
        }

        // 创建匹配结果数组
        const matches = [];
        const textLower = text.toLowerCase();
        
        // 预处理关键词，创建索引映射
        const keywordData = this._preprocessKeywordsForMatching(keywords, textLower);
        
        // 如果没有候选关键词，直接返回空数组
        if (keywordData.length === 0) {
          this._addToMatchCache(cacheKey, matches);
          return matches;
        }

        // 使用优化的多模式匹配算法
        const allMatches = this._multiPatternMatch(text, textLower, keywordData);
        
        // 处理重叠，选择最长匹配
        const finalMatches = this._resolveOverlaps(allMatches);
        
        // 添加到缓存
        this._addToMatchCache(cacheKey, finalMatches);
        
        return finalMatches;
      } catch (error) {
        Utils.handleError(error, "findMatches", "HIGHLIGHT");
        return [];
      }
    };

    // 添加新方法：生成匹配缓存键
    window.highlighter._generateMatchCacheKey = function(text, keywords) {
      // 使用文本长度和前50个字符作为键的一部分
      const textKey = `${text.length}_${text.substring(0, 50)}`;
      const keywordsKey = keywords.map(k => 
        typeof k === 'object' ? k.words : k
      ).join('|');
      return `${textKey}_${keywordsKey}`;
    };

    // 添加新方法：预处理关键词用于匹配
    window.highlighter._preprocessKeywordsForMatching = function(keywords, textLower) {
      const keywordData = [];
      
      for (const keyword of keywords) {
        const word = typeof keyword === 'object' ? keyword.words : keyword;
        if (!word) continue;
        
        const wordLower = word.toLowerCase();
        
        // 快速检查：如果文本中不包含关键词的第一个字符，跳过
        if (!textLower.includes(wordLower.charAt(0))) {
          continue;
        }
        
        keywordData.push({
          original: keyword,
          word: word,
          wordLower: wordLower,
          length: word.length
        });
      }
      
      // 按长度降序排序，优先匹配长关键词
      return keywordData.sort((a, b) => b.length - a.length);
    };

    // 添加新方法：多模式匹配算法
    window.highlighter._multiPatternMatch = function(text, textLower, keywordData) {
      const allMatches = [];
      
      // 对于少量关键词，使用优化的单独搜索
      if (keywordData.length <= 5) {
        for (const data of keywordData) {
          const positions = this._findAllOccurrencesOptimized(
            text, 
            textLower, 
            data.word, 
            data.wordLower
          );
          
          for (const pos of positions) {
            allMatches.push({
              start: pos,
              end: pos + data.length,
              text: text.slice(pos, pos + data.length),
              keyword: data.original,
              length: data.length
            });
          }
        }
      } else {
        // 对于大量关键词，使用Aho-Corasick算法的简化版本
        const matches = this._ahoCorasickLite(text, textLower, keywordData);
        allMatches.push(...matches);
      }
      
      return allMatches;
    };

    // 添加新方法：优化的查找所有出现位置
    window.highlighter._findAllOccurrencesOptimized = function(text, textLower, pattern, patternLower) {
      const positions = [];
      let index = 0;
      const maxMatches = 100;
      
      // 使用不区分大小写的搜索
      while ((index = textLower.indexOf(patternLower, index)) !== -1) {
        // 验证原始文本的大小写匹配（如果需要）
        if (this.options.caseSensitive) {
          const actualText = text.substr(index, pattern.length);
          if (actualText === pattern) {
            positions.push(index);
          }
        } else {
          positions.push(index);
        }
        
        index += 1; // 继续搜索下一个位置
        
        if (positions.length >= maxMatches) break;
      }
      
      return positions;
    };

    // 添加新方法：简化的Aho-Corasick算法实现
    window.highlighter._ahoCorasickLite = function(text, textLower, keywordData) {
      const matches = [];
      const textLength = text.length;
      
      // 创建关键词的第一个字符索引
      const firstCharIndex = new Map();
      for (const data of keywordData) {
        const firstChar = data.wordLower.charAt(0);
        if (!firstCharIndex.has(firstChar)) {
          firstCharIndex.set(firstChar, []);
        }
        firstCharIndex.get(firstChar).push(data);
      }
      
      // 扫描文本
      for (let i = 0; i < textLength; i++) {
        const char = textLower.charAt(i);
        const candidates = firstCharIndex.get(char);
        
        if (!candidates) continue;
        
        // 检查每个候选关键词
        for (const data of candidates) {
          if (i + data.length > textLength) continue;
          
          // 快速比较
          let match = true;
          for (let j = 0; j < data.length; j++) {
            if (textLower.charAt(i + j) !== data.wordLower.charAt(j)) {
              match = false;
              break;
            }
          }
          
          if (match) {
            // 如果需要大小写敏感，再次验证
            if (this.options.caseSensitive) {
              const actualText = text.substr(i, data.length);
              if (actualText !== data.word) continue;
            }
            
            matches.push({
              start: i,
              end: i + data.length,
              text: text.substr(i, data.length),
              keyword: data.original,
              length: data.length
            });
          }
        }
      }
      
      return matches;
    };

    // 添加新方法：解决重叠问题
    window.highlighter._resolveOverlaps = function(matches) {
      if (matches.length <= 1) return matches;
      
      // 按开始位置排序
      matches.sort((a, b) => a.start - b.start);
      
      const result = [];
      let lastEnd = -1;
      
      for (const match of matches) {
        // 如果不重叠，直接添加
        if (match.start >= lastEnd) {
          result.push(match);
          lastEnd = match.end;
        } else if (match.end > lastEnd) {
          // 如果部分重叠，选择更长的匹配
          const lastMatch = result[result.length - 1];
          if (match.length > lastMatch.length) {
            // 替换为更长的匹配
            result[result.length - 1] = match;
            lastEnd = match.end;
          }
        }
      }
      
      return result;
    };

    // 添加新方法：添加到匹配缓存
    window.highlighter._addToMatchCache = function(key, matches) {
      // LRU缓存管理
      if (this.matchCache.size >= this.MAX_MATCH_CACHE) {
        const oldestKey = this.matchCacheOrder.shift();
        this.matchCache.delete(oldestKey);
      }
      
      this.matchCache.set(key, matches);
      this.matchCacheOrder.push(key);
    };

    console.log('Performance patch applied successfully!');
  };

  // 开始应用补丁
  patchHighlighter();
})();
